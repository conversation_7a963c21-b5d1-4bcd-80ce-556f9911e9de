<?php
require_once 'order.php';
require_once 'config.php';

// 1. Authenticate and get token
function getPaymobToken() {
    $data = ['api_key' => PAYMOB_API_KEY];
    $response = file_get_contents(PAYMOB_API_URL . '/auth/tokens', false, stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/json\r\n",
            'content' => json_encode($data)
        ]
    ]));
    $result = json_decode($response, true);
    return $result['token'] ?? null;
}

// 2. Create session and get clientSecret
function createUnifiedSession($token, $order_id, $amount_cents, $billing_data, $publicKey) {
    $data = [
        'order_id' => $order_id,
        'amount_cents' => $amount_cents,
        'currency' => 'EGP',
        'billing_data' => $billing_data,
        'public_key' => $publicKey
    ];
    $response = file_get_contents(PAYMOB_API_URL . '/acceptance/unified_checkout/sessions', false, stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/json\r\nAuthorization: Bearer $token\r\n",
            'content' => json_encode($data)
        ]
    ]));
    $result = json_decode($response, true);
    return $result['client_secret'] ?? null;
}

// Collect user data (replace with real POST data in production)
$billing_data = [
    "apartment" => "NA",
    "email" => "<EMAIL>",
    "floor" => "NA",
    "first_name" => "John",
    "street" => "NA",
    "building" => "NA",
    "phone_number" => "0123456789", // REQUIRED
    "shipping_method" => "NA",
    "postal_code" => "NA",
    "city" => "Cairo",
    "country" => "EG",
    "last_name" => "Doe",
    "state" => "NA"
];

$amount_cents = 10000; // 100 EGP
$merchant_order_id = uniqid('order_');
$publicKey = PAYMOB_PUBLIC_KEY;

$token = getPaymobToken();
if (!$token) die('Auth failed');

$order_id = registerOrder($token, $amount_cents, $merchant_order_id);
if (!$order_id) die('Order registration failed');

$clientSecret = createUnifiedSession($token, $order_id, $amount_cents, $billing_data, $publicKey);
if (!$clientSecret) die('Unified session creation failed');

$unified_link_url = "https://accept.paymob.com/unifiedcheckout/?publicKey=$publicKey&clientSecret=$clientSecret";
header("Location: $unified_link_url");
exit;
?>