<?php
require_once 'config.php';

function generatePaymentKey($token, $order_id, $amount_cents, $billing_data) {
    $data = [
        'auth_token' => $token,
        'amount_cents' => $amount_cents,
        'expiration' => 3600,
        'order_id' => $order_id,
        'billing_data' => $billing_data,
        'currency' => 'EGP',
        'integration_id' => PAYMOB_INTEGRATION_ID,
        'lock_order_when_paid' => true
    ];
    $response = file_get_contents(PAYMOB_API_URL . '/acceptance/payment_keys', false, stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/json\r\n",
            'content' => json_encode($data)
        ]
    ]));
    $result = json_decode($response, true);
    return $result['token'] ?? null;
}
?>