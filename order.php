<?php
require_once 'auth.php';

function registerOrder($token, $amount_cents, $merchant_order_id) {
    $data = [
        'auth_token' => $token,
        'delivery_needed' => false,
        'amount_cents' => $amount_cents,
        'currency' => 'EGP',
        'merchant_order_id' => $merchant_order_id,
        'items' => []
    ];
    $response = file_get_contents(PAYMOB_API_URL . '/ecommerce/orders', false, stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/json\r\n",
            'content' => json_encode($data)
        ]
    ]));
    $result = json_decode($response, true);
    return $result['id'] ?? null;
}
?>